// This file is automatically generated by Keystone, do not modify it manually.
// Modify your Keystone config when you want to change this.

datasource mysql {
  url               = env("DATABASE_URL")
  shadowDatabaseUrl = env("SHADOW_DATABASE_URL")
  provider          = "mysql"
}

generator client {
  provider = "prisma-client-js"
}

model User {
  id                      String    @id @default(uuid())
  name                    String    @default("")
  nameLast                String    @default("")
  email                   String    @unique @default("")
  password                String
  isAdmin                 <PERSON>   @default(false)
  isActive                Boolean   @default(true)
  blog                    Blog[]    @relation("Blog_author")
  job                     Job?      @relation("Job_user")
  role                    Role?     @relation("User_role", fields: [roleId], references: [id])
  roleId                  String?   @map("role")
  createdAt               DateTime? @default(now())
  dateModified            DateTime? @default(now())
  passwordResetToken      String?
  passwordResetIssuedAt   DateTime?
  passwordResetRedeemedAt DateTime?

  @@index([roleId])
}

model Blog {
  id           String       @id @default(uuid())
  title        String       @default("")
  slug         String       @unique @default("")
  dateCreated  DateTime     @default(now())
  dateModified DateTime?    @default(now()) @updatedAt
  status       String?      @default("DRAFT")
  photo        MediaGalery? @relation("Blog_photo", fields: [photoId], references: [id])
  photoId      String?      @unique @map("photo")
  content      Json         @default("[{\"type\":\"paragraph\",\"children\":[{\"text\":\"\"}]}]")
  author       User?        @relation("Blog_author", fields: [authorId], references: [id])
  authorId     String?      @map("author")
  categories   Category[]   @relation("Blog_categories")
  tags         Tag[]        @relation("Blog_tags")
  language     Language?    @relation("Blog_language", fields: [languageId], references: [id])
  languageId   String?      @map("language")

  @@index([authorId])
  @@index([languageId])
}

model Tag {
  id   String @id @default(uuid())
  name String @unique @default("")
  blog Blog[] @relation("Blog_tags")
}

model Category {
  id                        String        @id @default(uuid())
  name                      String        @default("")
  nameIT                    String        @default("")
  excerpt                   String        @default("")
  excerptIT                 String        @default("")
  blog                      Blog[]        @relation("Blog_categories")
  jobCategories             JobCategory[] @relation("JobCategory_category")
  from_SubCategory_category SubCategory[] @relation("SubCategory_category")
}

model SubCategory {
  id                             String        @id @default(uuid())
  name                           String        @default("")
  nameIT                         String        @default("")
  category                       Category?     @relation("SubCategory_category", fields: [categoryId], references: [id])
  categoryId                     String?       @map("category")
  from_JobCategory_subcategories JobCategory[] @relation("JobCategory_subcategories")

  @@index([categoryId])
}

model JobCategory {
  id            String        @id @default(uuid())
  name          String        @default("")
  nameIT        String        @default("")
  category      Category?     @relation("JobCategory_category", fields: [categoryId], references: [id])
  categoryId    String?       @map("category")
  subcategories SubCategory[] @relation("JobCategory_subcategories")
  jobs          Job[]         @relation("Job_jobCategory")

  @@index([categoryId])
}

model Job {
  id            String           @id @default(uuid())
  title         String           @default("")
  company       String           @default("")
  salary        String           @default("")
  date          DateTime?        @mysql.Date
  jobCategory   JobCategory?     @relation("Job_jobCategory", fields: [jobCategoryId], references: [id])
  jobCategoryId String?          @map("jobCategory")
  location      Location?        @relation("Job_location", fields: [locationId], references: [id])
  locationId    String?          @map("location")
  description   String           @default("")
  requierments  String           @default("")
  whyWork       String           @default("")
  applyForm     JobApplication[] @relation("JobApplication_job")
  language      Language?        @relation("Job_language", fields: [languageId], references: [id])
  languageId    String?          @map("language")
  user          User?            @relation("Job_user", fields: [userId], references: [id])
  userId        String?          @unique @map("user")
  createdAt     DateTime?        @default(now())

  @@index([jobCategoryId])
  @@index([locationId])
  @@index([languageId])
}

model JobApplication {
  id          String         @id @default(uuid())
  name        String         @default("")
  email       String         @default("")
  message     String         @default("")
  birthDate   DateTime       @mysql.Date
  phone       String         @default("")
  createdAt   DateTime?      @default(now())
  job         Job?           @relation("JobApplication_job", fields: [jobId], references: [id])
  jobId       String?        @map("job")
  transport   TransportForm? @relation("JobApplication_transport", fields: [transportId], references: [id])
  transportId String?        @unique @map("transport")
  medical     MedicalForm?   @relation("JobApplication_medical", fields: [medicalId], references: [id])
  medicalId   String?        @unique @map("medical")

  @@index([jobId])
}

model MediaGalery {
  id       String @id
  image    Json?
  altText  String @default("")
  filename String @unique @default("")
  blog     Blog?  @relation("Blog_photo")
}

model Language {
  id        String @id @default(uuid())
  languages String @unique @default("RO")
  blog      Blog[] @relation("Blog_language")
  job       Job[]  @relation("Job_language")
}

model ContactForm {
  id      String @id @default(uuid())
  name    String @default("")
  email   String @default("")
  phone   String @default("")
  message String @default("")
}

model MedicalForm {
  id              String          @id @default(uuid())
  domeniu         String          @default("")
  subDomeniu      String          @default("")
  experienta      String          @default("")
  bac             String          @default("")
  amg             String          @default("")
  absolvire       String          @default("")
  experientaLimba String          @default("")
  locatia         String          @default("")
  ultimuSalar     Int
  cursItaliana    String          @default("")
  jobApplication  JobApplication? @relation("JobApplication_medical")
  phone           String          @default("")
  createdAt       DateTime?       @default(now())
}

model TransportForm {
  id              String          @id @default(uuid())
  domeniu         String          @default("")
  subDomeniu      String          @default("")
  experienta      String          @default("")
  locatia         String          @default("")
  tahograf        String          @default("")
  echipa          String          @default("")
  turaNoapte      String          @default("")
  experientaLimba String          @default("")
  ultimuSalar     Int
  salariuDorit    Int
  jobApplication  JobApplication? @relation("JobApplication_transport")
  phone           String          @default("")
  createdAt       DateTime?       @default(now())
}

model EmployerForm {
  id          String @id @default(uuid())
  domeniu     String @default("")
  subDomeniu  String @default("")
  codFiscal   String @default("")
  nrPersoane  String @default("")
  dateContact String @default("")
  email       String @default("")
  nrTel       Int
}

model Role {
  id                       String  @id @default(uuid())
  name                     String  @default("")
  canManageJobs            Boolean @default(false)
  canManageBlogs           Boolean @default(false)
  canManageLanguages       Boolean @default(false)
  canManageTags            Boolean @default(false)
  canManageJobApplications Boolean @default(false)
  canManageContactForms    Boolean @default(false)
  canManageCategories      Boolean @default(false)
  canSeeOtherUsers         Boolean @default(false)
  canManageUsers           Boolean @default(false)
  canManageRoles           Boolean @default(false)
  canManageWorkerForms     Boolean @default(false)
  canManageEmployerForms   Boolean @default(false)
  canManageLocations       Boolean @default(false)
  assignedTo               User[]  @relation("User_role")
}

model Location {
  id      String    @id @default(uuid())
  name    String    @default("")
  nameIT  String    @default("")
  country Country[] @relation("Location_country")
  cityRO  CityRO[]  @relation("Location_cityRO")
  cityIT  CityIT[]  @relation("Location_cityIT")
  zone    String?
  jobs    Job[]     @relation("Job_location")
}

model Country {
  id                    String     @id @default(uuid())
  name                  String     @default("")
  nameIT                String     @default("")
  from_Location_country Location[] @relation("Location_country")
}

model CityRO {
  id                   String     @id @default(uuid())
  name                 String     @default("")
  nameIT               String     @default("")
  createdAt            DateTime   @default(now())
  from_Location_cityRO Location[] @relation("Location_cityRO")

  @@index([createdAt])
}

model CityIT {
  id                   String     @id @default(uuid())
  name                 String     @default("")
  nameIT               String     @default("")
  createdAt            DateTime   @default(now())
  from_Location_cityIT Location[] @relation("Location_cityIT")

  @@index([createdAt])
}
