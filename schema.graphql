# This file is automatically generated by Keystone, do not modify it manually.
# Modify your Keystone config when you want to change this.

type User {
  id: ID!
  name: String
  nameLast: String
  email: String
  password: PasswordState
  isAdmin: Boolean
  isActive: Boolean
  blog(where: BlogWhereInput! = {}, orderBy: [BlogOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: BlogWhereUniqueInput): [Blog!]
  blogCount(where: BlogWhereInput! = {}): Int
  job: Job
  role: Role
  createdAt: DateTime
  dateModified: DateTime
  passwordResetToken: PasswordState
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

type PasswordState {
  isSet: Boolean!
}

scalar DateTime @specifiedBy(url: "https://datatracker.ietf.org/doc/html/rfc3339#section-5.6")

input UserWhereUniqueInput {
  id: ID
  email: String
}

input UserWhereInput {
  AND: [UserWhereInput!]
  OR: [UserWhereInput!]
  NOT: [UserWhereInput!]
  id: IDFilter
  name: StringFilter
  nameLast: StringFilter
  email: StringFilter
  isAdmin: BooleanFilter
  isActive: BooleanFilter
  blog: BlogManyRelationFilter
  job: JobWhereInput
  role: RoleWhereInput
  createdAt: DateTimeNullableFilter
  dateModified: DateTimeNullableFilter
  passwordResetToken: PasswordFilter
  passwordResetIssuedAt: DateTimeNullableFilter
  passwordResetRedeemedAt: DateTimeNullableFilter
}

input IDFilter {
  equals: ID
  in: [ID!]
  notIn: [ID!]
  lt: ID
  lte: ID
  gt: ID
  gte: ID
  not: IDFilter
}

input StringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: NestedStringFilter
}

input NestedStringFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: NestedStringFilter
}

input BooleanFilter {
  equals: Boolean
  not: BooleanFilter
}

input BlogManyRelationFilter {
  every: BlogWhereInput
  some: BlogWhereInput
  none: BlogWhereInput
}

input DateTimeNullableFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeNullableFilter
}

input PasswordFilter {
  isSet: Boolean!
}

input UserOrderByInput {
  id: OrderDirection
  name: OrderDirection
  nameLast: OrderDirection
  email: OrderDirection
  isAdmin: OrderDirection
  isActive: OrderDirection
  createdAt: OrderDirection
  dateModified: OrderDirection
  passwordResetIssuedAt: OrderDirection
  passwordResetRedeemedAt: OrderDirection
}

enum OrderDirection {
  asc
  desc
}

input UserUpdateInput {
  name: String
  nameLast: String
  email: String
  password: String
  isAdmin: Boolean
  isActive: Boolean
  blog: BlogRelateToManyForUpdateInput
  job: JobRelateToOneForUpdateInput
  role: RoleRelateToOneForUpdateInput
  createdAt: DateTime
  dateModified: DateTime
  passwordResetToken: String
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

input BlogRelateToManyForUpdateInput {
  disconnect: [BlogWhereUniqueInput!]
  set: [BlogWhereUniqueInput!]
  create: [BlogCreateInput!]
  connect: [BlogWhereUniqueInput!]
}

input JobRelateToOneForUpdateInput {
  create: JobCreateInput
  connect: JobWhereUniqueInput
  disconnect: Boolean
}

input RoleRelateToOneForUpdateInput {
  create: RoleCreateInput
  connect: RoleWhereUniqueInput
  disconnect: Boolean
}

input UserUpdateArgs {
  where: UserWhereUniqueInput!
  data: UserUpdateInput!
}

input UserCreateInput {
  name: String
  nameLast: String
  email: String
  password: String
  isAdmin: Boolean
  isActive: Boolean
  blog: BlogRelateToManyForCreateInput
  job: JobRelateToOneForCreateInput
  role: RoleRelateToOneForCreateInput
  createdAt: DateTime
  dateModified: DateTime
  passwordResetToken: String
  passwordResetIssuedAt: DateTime
  passwordResetRedeemedAt: DateTime
}

input BlogRelateToManyForCreateInput {
  create: [BlogCreateInput!]
  connect: [BlogWhereUniqueInput!]
}

input JobRelateToOneForCreateInput {
  create: JobCreateInput
  connect: JobWhereUniqueInput
}

input RoleRelateToOneForCreateInput {
  create: RoleCreateInput
  connect: RoleWhereUniqueInput
}

type Blog {
  id: ID!
  title: String
  slug: String
  dateCreated: DateTime
  dateModified: DateTime
  status: String
  photo: MediaGalery
  content: Blog_content_Document
  author: User
  categories(where: CategoryWhereInput! = {}, orderBy: [CategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CategoryWhereUniqueInput): [Category!]
  categoriesCount(where: CategoryWhereInput! = {}): Int
  tags(where: TagWhereInput! = {}, orderBy: [TagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TagWhereUniqueInput): [Tag!]
  tagsCount(where: TagWhereInput! = {}): Int
  language: Language
}

type Blog_content_Document {
  document(hydrateRelationships: Boolean! = false): JSON!
}

input BlogWhereUniqueInput {
  id: ID
  slug: String
}

input BlogWhereInput {
  AND: [BlogWhereInput!]
  OR: [BlogWhereInput!]
  NOT: [BlogWhereInput!]
  id: IDFilter
  title: StringFilter
  slug: StringFilter
  dateCreated: DateTimeFilter
  dateModified: DateTimeNullableFilter
  status: StringNullableFilter
  photo: MediaGaleryWhereInput
  author: UserWhereInput
  categories: CategoryManyRelationFilter
  tags: TagManyRelationFilter
  language: LanguageWhereInput
}

input DateTimeFilter {
  equals: DateTime
  in: [DateTime!]
  notIn: [DateTime!]
  lt: DateTime
  lte: DateTime
  gt: DateTime
  gte: DateTime
  not: DateTimeFilter
}

input StringNullableFilter {
  equals: String
  in: [String!]
  notIn: [String!]
  lt: String
  lte: String
  gt: String
  gte: String
  contains: String
  startsWith: String
  endsWith: String
  not: StringNullableFilter
}

input CategoryManyRelationFilter {
  every: CategoryWhereInput
  some: CategoryWhereInput
  none: CategoryWhereInput
}

input TagManyRelationFilter {
  every: TagWhereInput
  some: TagWhereInput
  none: TagWhereInput
}

input BlogOrderByInput {
  id: OrderDirection
  title: OrderDirection
  slug: OrderDirection
  dateCreated: OrderDirection
  dateModified: OrderDirection
  status: OrderDirection
}

input BlogUpdateInput {
  title: String
  slug: String
  dateCreated: DateTime
  dateModified: DateTime
  status: String
  photo: MediaGaleryRelateToOneForUpdateInput
  content: JSON
  author: UserRelateToOneForUpdateInput
  categories: CategoryRelateToManyForUpdateInput
  tags: TagRelateToManyForUpdateInput
  language: LanguageRelateToOneForUpdateInput
}

input MediaGaleryRelateToOneForUpdateInput {
  create: MediaGaleryCreateInput
  connect: MediaGaleryWhereUniqueInput
  disconnect: Boolean
}

input UserRelateToOneForUpdateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
  disconnect: Boolean
}

input CategoryRelateToManyForUpdateInput {
  disconnect: [CategoryWhereUniqueInput!]
  set: [CategoryWhereUniqueInput!]
  create: [CategoryCreateInput!]
  connect: [CategoryWhereUniqueInput!]
}

input TagRelateToManyForUpdateInput {
  disconnect: [TagWhereUniqueInput!]
  set: [TagWhereUniqueInput!]
  create: [TagCreateInput!]
  connect: [TagWhereUniqueInput!]
}

input LanguageRelateToOneForUpdateInput {
  create: LanguageCreateInput
  connect: LanguageWhereUniqueInput
  disconnect: Boolean
}

input BlogUpdateArgs {
  where: BlogWhereUniqueInput!
  data: BlogUpdateInput!
}

input BlogCreateInput {
  title: String
  slug: String
  dateCreated: DateTime
  dateModified: DateTime
  status: String
  photo: MediaGaleryRelateToOneForCreateInput
  content: JSON
  author: UserRelateToOneForCreateInput
  categories: CategoryRelateToManyForCreateInput
  tags: TagRelateToManyForCreateInput
  language: LanguageRelateToOneForCreateInput
}

input MediaGaleryRelateToOneForCreateInput {
  create: MediaGaleryCreateInput
  connect: MediaGaleryWhereUniqueInput
}

input UserRelateToOneForCreateInput {
  create: UserCreateInput
  connect: UserWhereUniqueInput
}

input CategoryRelateToManyForCreateInput {
  create: [CategoryCreateInput!]
  connect: [CategoryWhereUniqueInput!]
}

input TagRelateToManyForCreateInput {
  create: [TagCreateInput!]
  connect: [TagWhereUniqueInput!]
}

input LanguageRelateToOneForCreateInput {
  create: LanguageCreateInput
  connect: LanguageWhereUniqueInput
}

type Tag {
  id: ID!
  name: String
  blog(where: BlogWhereInput! = {}, orderBy: [BlogOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: BlogWhereUniqueInput): [Blog!]
  blogCount(where: BlogWhereInput! = {}): Int
}

input TagWhereUniqueInput {
  id: ID
  name: String
}

input TagWhereInput {
  AND: [TagWhereInput!]
  OR: [TagWhereInput!]
  NOT: [TagWhereInput!]
  id: IDFilter
  name: StringFilter
  blog: BlogManyRelationFilter
}

input TagOrderByInput {
  id: OrderDirection
  name: OrderDirection
}

input TagUpdateInput {
  name: String
  blog: BlogRelateToManyForUpdateInput
}

input TagUpdateArgs {
  where: TagWhereUniqueInput!
  data: TagUpdateInput!
}

input TagCreateInput {
  name: String
  blog: BlogRelateToManyForCreateInput
}

type Category {
  id: ID!
  name: String
  nameIT: String
  excerpt: String
  excerptIT: String
  blog(where: BlogWhereInput! = {}, orderBy: [BlogOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: BlogWhereUniqueInput): [Blog!]
  blogCount(where: BlogWhereInput! = {}): Int
  jobCategories(where: JobCategoryWhereInput! = {}, orderBy: [JobCategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: JobCategoryWhereUniqueInput): [JobCategory!]
  jobCategoriesCount(where: JobCategoryWhereInput! = {}): Int
}

input CategoryWhereUniqueInput {
  id: ID
}

input CategoryWhereInput {
  AND: [CategoryWhereInput!]
  OR: [CategoryWhereInput!]
  NOT: [CategoryWhereInput!]
  id: IDFilter
  name: StringFilter
  nameIT: StringFilter
  excerpt: StringFilter
  excerptIT: StringFilter
  blog: BlogManyRelationFilter
  jobCategories: JobCategoryManyRelationFilter
}

input JobCategoryManyRelationFilter {
  every: JobCategoryWhereInput
  some: JobCategoryWhereInput
  none: JobCategoryWhereInput
}

input CategoryOrderByInput {
  id: OrderDirection
  name: OrderDirection
  nameIT: OrderDirection
  excerpt: OrderDirection
  excerptIT: OrderDirection
}

input CategoryUpdateInput {
  name: String
  nameIT: String
  excerpt: String
  excerptIT: String
  blog: BlogRelateToManyForUpdateInput
  jobCategories: JobCategoryRelateToManyForUpdateInput
}

input JobCategoryRelateToManyForUpdateInput {
  disconnect: [JobCategoryWhereUniqueInput!]
  set: [JobCategoryWhereUniqueInput!]
  create: [JobCategoryCreateInput!]
  connect: [JobCategoryWhereUniqueInput!]
}

input CategoryUpdateArgs {
  where: CategoryWhereUniqueInput!
  data: CategoryUpdateInput!
}

input CategoryCreateInput {
  name: String
  nameIT: String
  excerpt: String
  excerptIT: String
  blog: BlogRelateToManyForCreateInput
  jobCategories: JobCategoryRelateToManyForCreateInput
}

input JobCategoryRelateToManyForCreateInput {
  create: [JobCategoryCreateInput!]
  connect: [JobCategoryWhereUniqueInput!]
}

type SubCategory {
  id: ID!
  name: String
  nameIT: String
  category: Category
}

input SubCategoryWhereUniqueInput {
  id: ID
}

input SubCategoryWhereInput {
  AND: [SubCategoryWhereInput!]
  OR: [SubCategoryWhereInput!]
  NOT: [SubCategoryWhereInput!]
  id: IDFilter
  name: StringFilter
  nameIT: StringFilter
  category: CategoryWhereInput
}

input SubCategoryOrderByInput {
  id: OrderDirection
  name: OrderDirection
  nameIT: OrderDirection
}

input SubCategoryUpdateInput {
  name: String
  nameIT: String
  category: CategoryRelateToOneForUpdateInput
}

input CategoryRelateToOneForUpdateInput {
  create: CategoryCreateInput
  connect: CategoryWhereUniqueInput
  disconnect: Boolean
}

input SubCategoryUpdateArgs {
  where: SubCategoryWhereUniqueInput!
  data: SubCategoryUpdateInput!
}

input SubCategoryCreateInput {
  name: String
  nameIT: String
  category: CategoryRelateToOneForCreateInput
}

input CategoryRelateToOneForCreateInput {
  create: CategoryCreateInput
  connect: CategoryWhereUniqueInput
}

type JobCategory {
  id: ID!
  name: String
  nameIT: String
  category: Category
  subcategories(where: SubCategoryWhereInput! = {}, orderBy: [SubCategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: SubCategoryWhereUniqueInput): [SubCategory!]
  subcategoriesCount(where: SubCategoryWhereInput! = {}): Int
  jobs(where: JobWhereInput! = {}, orderBy: [JobOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: JobWhereUniqueInput): [Job!]
  jobsCount(where: JobWhereInput! = {}): Int
}

input JobCategoryWhereUniqueInput {
  id: ID
}

input JobCategoryWhereInput {
  AND: [JobCategoryWhereInput!]
  OR: [JobCategoryWhereInput!]
  NOT: [JobCategoryWhereInput!]
  id: IDFilter
  name: StringFilter
  nameIT: StringFilter
  category: CategoryWhereInput
  subcategories: SubCategoryManyRelationFilter
  jobs: JobManyRelationFilter
}

input SubCategoryManyRelationFilter {
  every: SubCategoryWhereInput
  some: SubCategoryWhereInput
  none: SubCategoryWhereInput
}

input JobManyRelationFilter {
  every: JobWhereInput
  some: JobWhereInput
  none: JobWhereInput
}

input JobCategoryOrderByInput {
  id: OrderDirection
  name: OrderDirection
  nameIT: OrderDirection
}

input JobCategoryUpdateInput {
  name: String
  nameIT: String
  category: CategoryRelateToOneForUpdateInput
  subcategories: SubCategoryRelateToManyForUpdateInput
  jobs: JobRelateToManyForUpdateInput
}

input SubCategoryRelateToManyForUpdateInput {
  disconnect: [SubCategoryWhereUniqueInput!]
  set: [SubCategoryWhereUniqueInput!]
  create: [SubCategoryCreateInput!]
  connect: [SubCategoryWhereUniqueInput!]
}

input JobRelateToManyForUpdateInput {
  disconnect: [JobWhereUniqueInput!]
  set: [JobWhereUniqueInput!]
  create: [JobCreateInput!]
  connect: [JobWhereUniqueInput!]
}

input JobCategoryUpdateArgs {
  where: JobCategoryWhereUniqueInput!
  data: JobCategoryUpdateInput!
}

input JobCategoryCreateInput {
  name: String
  nameIT: String
  category: CategoryRelateToOneForCreateInput
  subcategories: SubCategoryRelateToManyForCreateInput
  jobs: JobRelateToManyForCreateInput
}

input SubCategoryRelateToManyForCreateInput {
  create: [SubCategoryCreateInput!]
  connect: [SubCategoryWhereUniqueInput!]
}

input JobRelateToManyForCreateInput {
  create: [JobCreateInput!]
  connect: [JobWhereUniqueInput!]
}

type Job {
  id: ID!
  title: String
  company: String
  salary: String
  date: CalendarDay
  jobCategory: JobCategory
  location: Location
  description: String
  requierments: String
  whyWork: String
  applyForm(where: JobApplicationWhereInput! = {}, orderBy: [JobApplicationOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: JobApplicationWhereUniqueInput): [JobApplication!]
  applyFormCount(where: JobApplicationWhereInput! = {}): Int
  language: Language
  user: User
  createdAt: DateTime
}

scalar CalendarDay @specifiedBy(url: "https://datatracker.ietf.org/doc/html/rfc3339#section-5.6")

input JobWhereUniqueInput {
  id: ID
}

input JobWhereInput {
  AND: [JobWhereInput!]
  OR: [JobWhereInput!]
  NOT: [JobWhereInput!]
  id: IDFilter
  title: StringFilter
  company: StringFilter
  salary: StringFilter
  date: CalendarDayNullableFilter
  jobCategory: JobCategoryWhereInput
  location: LocationWhereInput
  description: StringFilter
  requierments: StringFilter
  whyWork: StringFilter
  applyForm: JobApplicationManyRelationFilter
  language: LanguageWhereInput
  user: UserWhereInput
  createdAt: DateTimeNullableFilter
}

input CalendarDayNullableFilter {
  equals: CalendarDay
  in: [CalendarDay!]
  notIn: [CalendarDay!]
  lt: CalendarDay
  lte: CalendarDay
  gt: CalendarDay
  gte: CalendarDay
  not: CalendarDayNullableFilter
}

input JobApplicationManyRelationFilter {
  every: JobApplicationWhereInput
  some: JobApplicationWhereInput
  none: JobApplicationWhereInput
}

input JobOrderByInput {
  id: OrderDirection
  title: OrderDirection
  company: OrderDirection
  salary: OrderDirection
  date: OrderDirection
  description: OrderDirection
  requierments: OrderDirection
  whyWork: OrderDirection
  createdAt: OrderDirection
}

input JobUpdateInput {
  title: String
  company: String
  salary: String
  date: CalendarDay
  jobCategory: JobCategoryRelateToOneForUpdateInput
  location: LocationRelateToOneForUpdateInput
  description: String
  requierments: String
  whyWork: String
  applyForm: JobApplicationRelateToManyForUpdateInput
  language: LanguageRelateToOneForUpdateInput
  user: UserRelateToOneForUpdateInput
  createdAt: DateTime
}

input JobCategoryRelateToOneForUpdateInput {
  create: JobCategoryCreateInput
  connect: JobCategoryWhereUniqueInput
  disconnect: Boolean
}

input LocationRelateToOneForUpdateInput {
  create: LocationCreateInput
  connect: LocationWhereUniqueInput
  disconnect: Boolean
}

input JobApplicationRelateToManyForUpdateInput {
  disconnect: [JobApplicationWhereUniqueInput!]
  set: [JobApplicationWhereUniqueInput!]
  create: [JobApplicationCreateInput!]
  connect: [JobApplicationWhereUniqueInput!]
}

input JobUpdateArgs {
  where: JobWhereUniqueInput!
  data: JobUpdateInput!
}

input JobCreateInput {
  title: String
  company: String
  salary: String
  date: CalendarDay
  jobCategory: JobCategoryRelateToOneForCreateInput
  location: LocationRelateToOneForCreateInput
  description: String
  requierments: String
  whyWork: String
  applyForm: JobApplicationRelateToManyForCreateInput
  language: LanguageRelateToOneForCreateInput
  user: UserRelateToOneForCreateInput
  createdAt: DateTime
}

input JobCategoryRelateToOneForCreateInput {
  create: JobCategoryCreateInput
  connect: JobCategoryWhereUniqueInput
}

input LocationRelateToOneForCreateInput {
  create: LocationCreateInput
  connect: LocationWhereUniqueInput
}

input JobApplicationRelateToManyForCreateInput {
  create: [JobApplicationCreateInput!]
  connect: [JobApplicationWhereUniqueInput!]
}

type JobApplication {
  id: ID!
  name: String
  email: String
  message: String
  birthDate: CalendarDay
  phone: String
  createdAt: DateTime
  job: Job
  transport: TransportForm
  medical: MedicalForm
}

input JobApplicationWhereUniqueInput {
  id: ID
}

input JobApplicationWhereInput {
  AND: [JobApplicationWhereInput!]
  OR: [JobApplicationWhereInput!]
  NOT: [JobApplicationWhereInput!]
  id: IDFilter
  name: StringFilter
  email: StringFilter
  message: StringFilter
  birthDate: CalendarDayFilter
  phone: StringFilter
  createdAt: DateTimeNullableFilter
  job: JobWhereInput
  transport: TransportFormWhereInput
  medical: MedicalFormWhereInput
}

input CalendarDayFilter {
  equals: CalendarDay
  in: [CalendarDay!]
  notIn: [CalendarDay!]
  lt: CalendarDay
  lte: CalendarDay
  gt: CalendarDay
  gte: CalendarDay
  not: CalendarDayFilter
}

input JobApplicationOrderByInput {
  id: OrderDirection
  name: OrderDirection
  email: OrderDirection
  message: OrderDirection
  birthDate: OrderDirection
  phone: OrderDirection
  createdAt: OrderDirection
}

input JobApplicationUpdateInput {
  name: String
  email: String
  message: String
  birthDate: CalendarDay
  phone: String
  createdAt: DateTime
  job: JobRelateToOneForUpdateInput
  transport: TransportFormRelateToOneForUpdateInput
  medical: MedicalFormRelateToOneForUpdateInput
}

input TransportFormRelateToOneForUpdateInput {
  create: TransportFormCreateInput
  connect: TransportFormWhereUniqueInput
  disconnect: Boolean
}

input MedicalFormRelateToOneForUpdateInput {
  create: MedicalFormCreateInput
  connect: MedicalFormWhereUniqueInput
  disconnect: Boolean
}

input JobApplicationUpdateArgs {
  where: JobApplicationWhereUniqueInput!
  data: JobApplicationUpdateInput!
}

input JobApplicationCreateInput {
  name: String
  email: String
  message: String
  birthDate: CalendarDay
  phone: String
  createdAt: DateTime
  job: JobRelateToOneForCreateInput
  transport: TransportFormRelateToOneForCreateInput
  medical: MedicalFormRelateToOneForCreateInput
}

input TransportFormRelateToOneForCreateInput {
  create: TransportFormCreateInput
  connect: TransportFormWhereUniqueInput
}

input MedicalFormRelateToOneForCreateInput {
  create: MedicalFormCreateInput
  connect: MedicalFormWhereUniqueInput
}

type MediaGalery {
  id: ID!
  image: CloudinaryImage_File
  altText: String
  filename: String
  blog: Blog
}

type CloudinaryImage_File {
  id: ID
  filename: String
  originalFilename: String
  mimetype: String
  encoding: String
  publicUrl: String
  publicUrlTransformed(transformation: CloudinaryImageFormat): String
}

"""
Mirrors the formatting options [Cloudinary provides](https://cloudinary.com/documentation/image_transformation_reference).
All options are strings as they ultimately end up in a URL.
"""
input CloudinaryImageFormat {
  """ Rewrites the filename to be this pretty string. Do not include `/` or `.`
  """
  prettyName: String
  width: String
  height: String
  crop: String
  aspect_ratio: String
  gravity: String
  zoom: String
  x: String
  y: String
  format: String
  fetch_format: String
  quality: String
  radius: String
  angle: String
  effect: String
  opacity: String
  border: String
  background: String
  overlay: String
  underlay: String
  default_image: String
  delay: String
  color: String
  color_space: String
  dpr: String
  page: String
  density: String
  flags: String
  transformation: String
}

input MediaGaleryWhereUniqueInput {
  id: ID
  filename: String
}

input MediaGaleryWhereInput {
  AND: [MediaGaleryWhereInput!]
  OR: [MediaGaleryWhereInput!]
  NOT: [MediaGaleryWhereInput!]
  id: IDFilter
  altText: StringFilter
  filename: StringFilter
  blog: BlogWhereInput
}

input MediaGaleryOrderByInput {
  id: OrderDirection
  altText: OrderDirection
  filename: OrderDirection
}

input MediaGaleryUpdateInput {
  image: Upload
  altText: String
  filename: String
  blog: BlogRelateToOneForUpdateInput
}

"""The `Upload` scalar type represents a file upload."""
scalar Upload

input BlogRelateToOneForUpdateInput {
  create: BlogCreateInput
  connect: BlogWhereUniqueInput
  disconnect: Boolean
}

input MediaGaleryUpdateArgs {
  where: MediaGaleryWhereUniqueInput!
  data: MediaGaleryUpdateInput!
}

input MediaGaleryCreateInput {
  image: Upload
  altText: String
  filename: String
  blog: BlogRelateToOneForCreateInput
}

input BlogRelateToOneForCreateInput {
  create: BlogCreateInput
  connect: BlogWhereUniqueInput
}

type Language {
  id: ID!
  languages: String
  blog(where: BlogWhereInput! = {}, orderBy: [BlogOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: BlogWhereUniqueInput): [Blog!]
  blogCount(where: BlogWhereInput! = {}): Int
  job(where: JobWhereInput! = {}, orderBy: [JobOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: JobWhereUniqueInput): [Job!]
  jobCount(where: JobWhereInput! = {}): Int
}

input LanguageWhereUniqueInput {
  id: ID
  languages: String
}

input LanguageWhereInput {
  AND: [LanguageWhereInput!]
  OR: [LanguageWhereInput!]
  NOT: [LanguageWhereInput!]
  id: IDFilter
  languages: StringFilter
  blog: BlogManyRelationFilter
  job: JobManyRelationFilter
}

input LanguageOrderByInput {
  id: OrderDirection
  languages: OrderDirection
}

input LanguageUpdateInput {
  languages: String
  blog: BlogRelateToManyForUpdateInput
  job: JobRelateToManyForUpdateInput
}

input LanguageUpdateArgs {
  where: LanguageWhereUniqueInput!
  data: LanguageUpdateInput!
}

input LanguageCreateInput {
  languages: String
  blog: BlogRelateToManyForCreateInput
  job: JobRelateToManyForCreateInput
}

type ContactForm {
  id: ID!
  name: String
  email: String
  phone: String
  message: String
}

input ContactFormWhereUniqueInput {
  id: ID
}

input ContactFormWhereInput {
  AND: [ContactFormWhereInput!]
  OR: [ContactFormWhereInput!]
  NOT: [ContactFormWhereInput!]
  id: IDFilter
  name: StringFilter
  email: StringFilter
  phone: StringFilter
  message: StringFilter
}

input ContactFormOrderByInput {
  id: OrderDirection
  name: OrderDirection
  email: OrderDirection
  phone: OrderDirection
  message: OrderDirection
}

input ContactFormUpdateInput {
  name: String
  email: String
  phone: String
  message: String
}

input ContactFormUpdateArgs {
  where: ContactFormWhereUniqueInput!
  data: ContactFormUpdateInput!
}

input ContactFormCreateInput {
  name: String
  email: String
  phone: String
  message: String
}

type MedicalForm {
  id: ID!
  domeniu: String
  subDomeniu: String
  experienta: String
  bac: String
  amg: String
  absolvire: String
  experientaLimba: String
  locatia: String
  ultimuSalar: Int
  cursItaliana: String
  jobApplication: JobApplication
  phone: String
  createdAt: DateTime
}

input MedicalFormWhereUniqueInput {
  id: ID
}

input MedicalFormWhereInput {
  AND: [MedicalFormWhereInput!]
  OR: [MedicalFormWhereInput!]
  NOT: [MedicalFormWhereInput!]
  id: IDFilter
  domeniu: StringFilter
  subDomeniu: StringFilter
  experienta: StringFilter
  bac: StringFilter
  amg: StringFilter
  absolvire: StringFilter
  experientaLimba: StringFilter
  locatia: StringFilter
  ultimuSalar: IntFilter
  cursItaliana: StringFilter
  jobApplication: JobApplicationWhereInput
  phone: StringFilter
  createdAt: DateTimeNullableFilter
}

input IntFilter {
  equals: Int
  in: [Int!]
  notIn: [Int!]
  lt: Int
  lte: Int
  gt: Int
  gte: Int
  not: IntFilter
}

input MedicalFormOrderByInput {
  id: OrderDirection
  domeniu: OrderDirection
  subDomeniu: OrderDirection
  experienta: OrderDirection
  bac: OrderDirection
  amg: OrderDirection
  absolvire: OrderDirection
  experientaLimba: OrderDirection
  locatia: OrderDirection
  ultimuSalar: OrderDirection
  cursItaliana: OrderDirection
  phone: OrderDirection
  createdAt: OrderDirection
}

input MedicalFormUpdateInput {
  domeniu: String
  subDomeniu: String
  experienta: String
  bac: String
  amg: String
  absolvire: String
  experientaLimba: String
  locatia: String
  ultimuSalar: Int
  cursItaliana: String
  jobApplication: JobApplicationRelateToOneForUpdateInput
  phone: String
  createdAt: DateTime
}

input JobApplicationRelateToOneForUpdateInput {
  create: JobApplicationCreateInput
  connect: JobApplicationWhereUniqueInput
  disconnect: Boolean
}

input MedicalFormUpdateArgs {
  where: MedicalFormWhereUniqueInput!
  data: MedicalFormUpdateInput!
}

input MedicalFormCreateInput {
  domeniu: String
  subDomeniu: String
  experienta: String
  bac: String
  amg: String
  absolvire: String
  experientaLimba: String
  locatia: String
  ultimuSalar: Int
  cursItaliana: String
  jobApplication: JobApplicationRelateToOneForCreateInput
  phone: String
  createdAt: DateTime
}

input JobApplicationRelateToOneForCreateInput {
  create: JobApplicationCreateInput
  connect: JobApplicationWhereUniqueInput
}

type TransportForm {
  id: ID!
  domeniu: String
  subDomeniu: String
  experienta: String
  locatia: String
  tahograf: String
  echipa: String
  turaNoapte: String
  experientaLimba: String
  ultimuSalar: Int
  salariuDorit: Int
  jobApplication: JobApplication
  phone: String
  createdAt: DateTime
}

input TransportFormWhereUniqueInput {
  id: ID
}

input TransportFormWhereInput {
  AND: [TransportFormWhereInput!]
  OR: [TransportFormWhereInput!]
  NOT: [TransportFormWhereInput!]
  id: IDFilter
  domeniu: StringFilter
  subDomeniu: StringFilter
  experienta: StringFilter
  locatia: StringFilter
  tahograf: StringFilter
  echipa: StringFilter
  turaNoapte: StringFilter
  experientaLimba: StringFilter
  ultimuSalar: IntFilter
  salariuDorit: IntFilter
  jobApplication: JobApplicationWhereInput
  phone: StringFilter
  createdAt: DateTimeNullableFilter
}

input TransportFormOrderByInput {
  id: OrderDirection
  domeniu: OrderDirection
  subDomeniu: OrderDirection
  experienta: OrderDirection
  locatia: OrderDirection
  tahograf: OrderDirection
  echipa: OrderDirection
  turaNoapte: OrderDirection
  experientaLimba: OrderDirection
  ultimuSalar: OrderDirection
  salariuDorit: OrderDirection
  phone: OrderDirection
  createdAt: OrderDirection
}

input TransportFormUpdateInput {
  domeniu: String
  subDomeniu: String
  experienta: String
  locatia: String
  tahograf: String
  echipa: String
  turaNoapte: String
  experientaLimba: String
  ultimuSalar: Int
  salariuDorit: Int
  jobApplication: JobApplicationRelateToOneForUpdateInput
  phone: String
  createdAt: DateTime
}

input TransportFormUpdateArgs {
  where: TransportFormWhereUniqueInput!
  data: TransportFormUpdateInput!
}

input TransportFormCreateInput {
  domeniu: String
  subDomeniu: String
  experienta: String
  locatia: String
  tahograf: String
  echipa: String
  turaNoapte: String
  experientaLimba: String
  ultimuSalar: Int
  salariuDorit: Int
  jobApplication: JobApplicationRelateToOneForCreateInput
  phone: String
  createdAt: DateTime
}

type EmployerForm {
  id: ID!
  domeniu: String
  subDomeniu: String
  codFiscal: String
  nrPersoane: String
  dateContact: String
  email: String
  nrTel: Int
}

input EmployerFormWhereUniqueInput {
  id: ID
}

input EmployerFormWhereInput {
  AND: [EmployerFormWhereInput!]
  OR: [EmployerFormWhereInput!]
  NOT: [EmployerFormWhereInput!]
  id: IDFilter
  domeniu: StringFilter
  subDomeniu: StringFilter
  codFiscal: StringFilter
  nrPersoane: StringFilter
  dateContact: StringFilter
  email: StringFilter
  nrTel: IntFilter
}

input EmployerFormOrderByInput {
  id: OrderDirection
  domeniu: OrderDirection
  subDomeniu: OrderDirection
  codFiscal: OrderDirection
  nrPersoane: OrderDirection
  dateContact: OrderDirection
  email: OrderDirection
  nrTel: OrderDirection
}

input EmployerFormUpdateInput {
  domeniu: String
  subDomeniu: String
  codFiscal: String
  nrPersoane: String
  dateContact: String
  email: String
  nrTel: Int
}

input EmployerFormUpdateArgs {
  where: EmployerFormWhereUniqueInput!
  data: EmployerFormUpdateInput!
}

input EmployerFormCreateInput {
  domeniu: String
  subDomeniu: String
  codFiscal: String
  nrPersoane: String
  dateContact: String
  email: String
  nrTel: Int
}

type Role {
  id: ID!
  name: String
  canManageJobs: Boolean
  canManageBlogs: Boolean
  canManageLanguages: Boolean
  canManageTags: Boolean
  canManageJobApplications: Boolean
  canManageContactForms: Boolean
  canManageCategories: Boolean
  canSeeOtherUsers: Boolean
  canManageUsers: Boolean
  canManageRoles: Boolean
  canManageWorkerForms: Boolean
  canManageEmployerForms: Boolean
  canManageLocations: Boolean
  assignedTo(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  assignedToCount(where: UserWhereInput! = {}): Int
}

input RoleWhereUniqueInput {
  id: ID
}

input RoleWhereInput {
  AND: [RoleWhereInput!]
  OR: [RoleWhereInput!]
  NOT: [RoleWhereInput!]
  id: IDFilter
  name: StringFilter
  canManageJobs: BooleanFilter
  canManageBlogs: BooleanFilter
  canManageLanguages: BooleanFilter
  canManageTags: BooleanFilter
  canManageJobApplications: BooleanFilter
  canManageContactForms: BooleanFilter
  canManageCategories: BooleanFilter
  canSeeOtherUsers: BooleanFilter
  canManageUsers: BooleanFilter
  canManageRoles: BooleanFilter
  canManageWorkerForms: BooleanFilter
  canManageEmployerForms: BooleanFilter
  canManageLocations: BooleanFilter
  assignedTo: UserManyRelationFilter
}

input UserManyRelationFilter {
  every: UserWhereInput
  some: UserWhereInput
  none: UserWhereInput
}

input RoleOrderByInput {
  id: OrderDirection
  name: OrderDirection
  canManageJobs: OrderDirection
  canManageBlogs: OrderDirection
  canManageLanguages: OrderDirection
  canManageTags: OrderDirection
  canManageJobApplications: OrderDirection
  canManageContactForms: OrderDirection
  canManageCategories: OrderDirection
  canSeeOtherUsers: OrderDirection
  canManageUsers: OrderDirection
  canManageRoles: OrderDirection
  canManageWorkerForms: OrderDirection
  canManageEmployerForms: OrderDirection
  canManageLocations: OrderDirection
}

input RoleUpdateInput {
  name: String
  canManageJobs: Boolean
  canManageBlogs: Boolean
  canManageLanguages: Boolean
  canManageTags: Boolean
  canManageJobApplications: Boolean
  canManageContactForms: Boolean
  canManageCategories: Boolean
  canSeeOtherUsers: Boolean
  canManageUsers: Boolean
  canManageRoles: Boolean
  canManageWorkerForms: Boolean
  canManageEmployerForms: Boolean
  canManageLocations: Boolean
  assignedTo: UserRelateToManyForUpdateInput
}

input UserRelateToManyForUpdateInput {
  disconnect: [UserWhereUniqueInput!]
  set: [UserWhereUniqueInput!]
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

input RoleUpdateArgs {
  where: RoleWhereUniqueInput!
  data: RoleUpdateInput!
}

input RoleCreateInput {
  name: String
  canManageJobs: Boolean
  canManageBlogs: Boolean
  canManageLanguages: Boolean
  canManageTags: Boolean
  canManageJobApplications: Boolean
  canManageContactForms: Boolean
  canManageCategories: Boolean
  canSeeOtherUsers: Boolean
  canManageUsers: Boolean
  canManageRoles: Boolean
  canManageWorkerForms: Boolean
  canManageEmployerForms: Boolean
  canManageLocations: Boolean
  assignedTo: UserRelateToManyForCreateInput
}

input UserRelateToManyForCreateInput {
  create: [UserCreateInput!]
  connect: [UserWhereUniqueInput!]
}

type Location {
  id: ID!
  name: String
  nameIT: String
  country(where: CountryWhereInput! = {}, orderBy: [CountryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CountryWhereUniqueInput): [Country!]
  countryCount(where: CountryWhereInput! = {}): Int
  cityRO(where: CityROWhereInput! = {}, orderBy: [CityROOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CityROWhereUniqueInput): [CityRO!]
  cityROCount(where: CityROWhereInput! = {}): Int
  cityIT(where: CityITWhereInput! = {}, orderBy: [CityITOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CityITWhereUniqueInput): [CityIT!]
  cityITCount(where: CityITWhereInput! = {}): Int
  zone: String
  jobs(where: JobWhereInput! = {}, orderBy: [JobOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: JobWhereUniqueInput): [Job!]
  jobsCount(where: JobWhereInput! = {}): Int
}

input LocationWhereUniqueInput {
  id: ID
}

input LocationWhereInput {
  AND: [LocationWhereInput!]
  OR: [LocationWhereInput!]
  NOT: [LocationWhereInput!]
  id: IDFilter
  name: StringFilter
  nameIT: StringFilter
  country: CountryManyRelationFilter
  cityRO: CityROManyRelationFilter
  cityIT: CityITManyRelationFilter
  zone: StringNullableFilter
  jobs: JobManyRelationFilter
}

input CountryManyRelationFilter {
  every: CountryWhereInput
  some: CountryWhereInput
  none: CountryWhereInput
}

input CityROManyRelationFilter {
  every: CityROWhereInput
  some: CityROWhereInput
  none: CityROWhereInput
}

input CityITManyRelationFilter {
  every: CityITWhereInput
  some: CityITWhereInput
  none: CityITWhereInput
}

input LocationOrderByInput {
  id: OrderDirection
  name: OrderDirection
  nameIT: OrderDirection
  zone: OrderDirection
}

input LocationUpdateInput {
  name: String
  nameIT: String
  country: CountryRelateToManyForUpdateInput
  cityRO: CityRORelateToManyForUpdateInput
  cityIT: CityITRelateToManyForUpdateInput
  zone: String
  jobs: JobRelateToManyForUpdateInput
}

input CountryRelateToManyForUpdateInput {
  disconnect: [CountryWhereUniqueInput!]
  set: [CountryWhereUniqueInput!]
  create: [CountryCreateInput!]
  connect: [CountryWhereUniqueInput!]
}

input CityRORelateToManyForUpdateInput {
  disconnect: [CityROWhereUniqueInput!]
  set: [CityROWhereUniqueInput!]
  create: [CityROCreateInput!]
  connect: [CityROWhereUniqueInput!]
}

input CityITRelateToManyForUpdateInput {
  disconnect: [CityITWhereUniqueInput!]
  set: [CityITWhereUniqueInput!]
  create: [CityITCreateInput!]
  connect: [CityITWhereUniqueInput!]
}

input LocationUpdateArgs {
  where: LocationWhereUniqueInput!
  data: LocationUpdateInput!
}

input LocationCreateInput {
  name: String
  nameIT: String
  country: CountryRelateToManyForCreateInput
  cityRO: CityRORelateToManyForCreateInput
  cityIT: CityITRelateToManyForCreateInput
  zone: String
  jobs: JobRelateToManyForCreateInput
}

input CountryRelateToManyForCreateInput {
  create: [CountryCreateInput!]
  connect: [CountryWhereUniqueInput!]
}

input CityRORelateToManyForCreateInput {
  create: [CityROCreateInput!]
  connect: [CityROWhereUniqueInput!]
}

input CityITRelateToManyForCreateInput {
  create: [CityITCreateInput!]
  connect: [CityITWhereUniqueInput!]
}

type Country {
  id: ID!
  name: String
  nameIT: String
}

input CountryWhereUniqueInput {
  id: ID
}

input CountryWhereInput {
  AND: [CountryWhereInput!]
  OR: [CountryWhereInput!]
  NOT: [CountryWhereInput!]
  id: IDFilter
  name: StringFilter
  nameIT: StringFilter
}

input CountryOrderByInput {
  id: OrderDirection
  name: OrderDirection
  nameIT: OrderDirection
}

input CountryUpdateInput {
  name: String
  nameIT: String
}

input CountryUpdateArgs {
  where: CountryWhereUniqueInput!
  data: CountryUpdateInput!
}

input CountryCreateInput {
  name: String
  nameIT: String
}

type CityRO {
  id: ID!
  name: String
  nameIT: String
  createdAt: DateTime
}

input CityROWhereUniqueInput {
  id: ID
}

input CityROWhereInput {
  AND: [CityROWhereInput!]
  OR: [CityROWhereInput!]
  NOT: [CityROWhereInput!]
  id: IDFilter
  name: StringFilter
  nameIT: StringFilter
  createdAt: DateTimeFilter
}

input CityROOrderByInput {
  id: OrderDirection
  name: OrderDirection
  nameIT: OrderDirection
  createdAt: OrderDirection
}

input CityROUpdateInput {
  name: String
  nameIT: String
  createdAt: DateTime
}

input CityROUpdateArgs {
  where: CityROWhereUniqueInput!
  data: CityROUpdateInput!
}

input CityROCreateInput {
  name: String
  nameIT: String
  createdAt: DateTime
}

type CityIT {
  id: ID!
  name: String
  nameIT: String
  createdAt: DateTime
}

input CityITWhereUniqueInput {
  id: ID
}

input CityITWhereInput {
  AND: [CityITWhereInput!]
  OR: [CityITWhereInput!]
  NOT: [CityITWhereInput!]
  id: IDFilter
  name: StringFilter
  nameIT: StringFilter
  createdAt: DateTimeFilter
}

input CityITOrderByInput {
  id: OrderDirection
  name: OrderDirection
  nameIT: OrderDirection
  createdAt: OrderDirection
}

input CityITUpdateInput {
  name: String
  nameIT: String
  createdAt: DateTime
}

input CityITUpdateArgs {
  where: CityITWhereUniqueInput!
  data: CityITUpdateInput!
}

input CityITCreateInput {
  name: String
  nameIT: String
  createdAt: DateTime
}

"""
The `JSON` scalar type represents JSON values as specified by [ECMA-404](http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf).
"""
scalar JSON @specifiedBy(url: "http://www.ecma-international.org/publications/files/ECMA-ST/ECMA-404.pdf")

type Mutation {
  createUser(data: UserCreateInput!): User
  createUsers(data: [UserCreateInput!]!): [User]
  updateUser(where: UserWhereUniqueInput!, data: UserUpdateInput!): User
  updateUsers(data: [UserUpdateArgs!]!): [User]
  deleteUser(where: UserWhereUniqueInput!): User
  deleteUsers(where: [UserWhereUniqueInput!]!): [User]
  createBlog(data: BlogCreateInput!): Blog
  createBlogs(data: [BlogCreateInput!]!): [Blog]
  updateBlog(where: BlogWhereUniqueInput!, data: BlogUpdateInput!): Blog
  updateBlogs(data: [BlogUpdateArgs!]!): [Blog]
  deleteBlog(where: BlogWhereUniqueInput!): Blog
  deleteBlogs(where: [BlogWhereUniqueInput!]!): [Blog]
  createTag(data: TagCreateInput!): Tag
  createTags(data: [TagCreateInput!]!): [Tag]
  updateTag(where: TagWhereUniqueInput!, data: TagUpdateInput!): Tag
  updateTags(data: [TagUpdateArgs!]!): [Tag]
  deleteTag(where: TagWhereUniqueInput!): Tag
  deleteTags(where: [TagWhereUniqueInput!]!): [Tag]
  createCategory(data: CategoryCreateInput!): Category
  createCategories(data: [CategoryCreateInput!]!): [Category]
  updateCategory(where: CategoryWhereUniqueInput!, data: CategoryUpdateInput!): Category
  updateCategories(data: [CategoryUpdateArgs!]!): [Category]
  deleteCategory(where: CategoryWhereUniqueInput!): Category
  deleteCategories(where: [CategoryWhereUniqueInput!]!): [Category]
  createSubCategory(data: SubCategoryCreateInput!): SubCategory
  createSubCategories(data: [SubCategoryCreateInput!]!): [SubCategory]
  updateSubCategory(where: SubCategoryWhereUniqueInput!, data: SubCategoryUpdateInput!): SubCategory
  updateSubCategories(data: [SubCategoryUpdateArgs!]!): [SubCategory]
  deleteSubCategory(where: SubCategoryWhereUniqueInput!): SubCategory
  deleteSubCategories(where: [SubCategoryWhereUniqueInput!]!): [SubCategory]
  createJobCategory(data: JobCategoryCreateInput!): JobCategory
  createJobCategories(data: [JobCategoryCreateInput!]!): [JobCategory]
  updateJobCategory(where: JobCategoryWhereUniqueInput!, data: JobCategoryUpdateInput!): JobCategory
  updateJobCategories(data: [JobCategoryUpdateArgs!]!): [JobCategory]
  deleteJobCategory(where: JobCategoryWhereUniqueInput!): JobCategory
  deleteJobCategories(where: [JobCategoryWhereUniqueInput!]!): [JobCategory]
  createJob(data: JobCreateInput!): Job
  createJobs(data: [JobCreateInput!]!): [Job]
  updateJob(where: JobWhereUniqueInput!, data: JobUpdateInput!): Job
  updateJobs(data: [JobUpdateArgs!]!): [Job]
  deleteJob(where: JobWhereUniqueInput!): Job
  deleteJobs(where: [JobWhereUniqueInput!]!): [Job]
  createJobApplication(data: JobApplicationCreateInput!): JobApplication
  createJobApplications(data: [JobApplicationCreateInput!]!): [JobApplication]
  updateJobApplication(where: JobApplicationWhereUniqueInput!, data: JobApplicationUpdateInput!): JobApplication
  updateJobApplications(data: [JobApplicationUpdateArgs!]!): [JobApplication]
  deleteJobApplication(where: JobApplicationWhereUniqueInput!): JobApplication
  deleteJobApplications(where: [JobApplicationWhereUniqueInput!]!): [JobApplication]
  createMediaGalery(data: MediaGaleryCreateInput!): MediaGalery
  createMediaGaleries(data: [MediaGaleryCreateInput!]!): [MediaGalery]
  updateMediaGalery(where: MediaGaleryWhereUniqueInput!, data: MediaGaleryUpdateInput!): MediaGalery
  updateMediaGaleries(data: [MediaGaleryUpdateArgs!]!): [MediaGalery]
  deleteMediaGalery(where: MediaGaleryWhereUniqueInput!): MediaGalery
  deleteMediaGaleries(where: [MediaGaleryWhereUniqueInput!]!): [MediaGalery]
  createLanguage(data: LanguageCreateInput!): Language
  createLanguages(data: [LanguageCreateInput!]!): [Language]
  updateLanguage(where: LanguageWhereUniqueInput!, data: LanguageUpdateInput!): Language
  updateLanguages(data: [LanguageUpdateArgs!]!): [Language]
  deleteLanguage(where: LanguageWhereUniqueInput!): Language
  deleteLanguages(where: [LanguageWhereUniqueInput!]!): [Language]
  createContactForm(data: ContactFormCreateInput!): ContactForm
  createContactForms(data: [ContactFormCreateInput!]!): [ContactForm]
  updateContactForm(where: ContactFormWhereUniqueInput!, data: ContactFormUpdateInput!): ContactForm
  updateContactForms(data: [ContactFormUpdateArgs!]!): [ContactForm]
  deleteContactForm(where: ContactFormWhereUniqueInput!): ContactForm
  deleteContactForms(where: [ContactFormWhereUniqueInput!]!): [ContactForm]
  createMedicalForm(data: MedicalFormCreateInput!): MedicalForm
  createMedicalForms(data: [MedicalFormCreateInput!]!): [MedicalForm]
  updateMedicalForm(where: MedicalFormWhereUniqueInput!, data: MedicalFormUpdateInput!): MedicalForm
  updateMedicalForms(data: [MedicalFormUpdateArgs!]!): [MedicalForm]
  deleteMedicalForm(where: MedicalFormWhereUniqueInput!): MedicalForm
  deleteMedicalForms(where: [MedicalFormWhereUniqueInput!]!): [MedicalForm]
  createTransportForm(data: TransportFormCreateInput!): TransportForm
  createTransportForms(data: [TransportFormCreateInput!]!): [TransportForm]
  updateTransportForm(where: TransportFormWhereUniqueInput!, data: TransportFormUpdateInput!): TransportForm
  updateTransportForms(data: [TransportFormUpdateArgs!]!): [TransportForm]
  deleteTransportForm(where: TransportFormWhereUniqueInput!): TransportForm
  deleteTransportForms(where: [TransportFormWhereUniqueInput!]!): [TransportForm]
  createEmployerForm(data: EmployerFormCreateInput!): EmployerForm
  createEmployerForms(data: [EmployerFormCreateInput!]!): [EmployerForm]
  updateEmployerForm(where: EmployerFormWhereUniqueInput!, data: EmployerFormUpdateInput!): EmployerForm
  updateEmployerForms(data: [EmployerFormUpdateArgs!]!): [EmployerForm]
  deleteEmployerForm(where: EmployerFormWhereUniqueInput!): EmployerForm
  deleteEmployerForms(where: [EmployerFormWhereUniqueInput!]!): [EmployerForm]
  createRole(data: RoleCreateInput!): Role
  createRoles(data: [RoleCreateInput!]!): [Role]
  updateRole(where: RoleWhereUniqueInput!, data: RoleUpdateInput!): Role
  updateRoles(data: [RoleUpdateArgs!]!): [Role]
  deleteRole(where: RoleWhereUniqueInput!): Role
  deleteRoles(where: [RoleWhereUniqueInput!]!): [Role]
  createLocation(data: LocationCreateInput!): Location
  createLocations(data: [LocationCreateInput!]!): [Location]
  updateLocation(where: LocationWhereUniqueInput!, data: LocationUpdateInput!): Location
  updateLocations(data: [LocationUpdateArgs!]!): [Location]
  deleteLocation(where: LocationWhereUniqueInput!): Location
  deleteLocations(where: [LocationWhereUniqueInput!]!): [Location]
  createCountry(data: CountryCreateInput!): Country
  createCountries(data: [CountryCreateInput!]!): [Country]
  updateCountry(where: CountryWhereUniqueInput!, data: CountryUpdateInput!): Country
  updateCountries(data: [CountryUpdateArgs!]!): [Country]
  deleteCountry(where: CountryWhereUniqueInput!): Country
  deleteCountries(where: [CountryWhereUniqueInput!]!): [Country]
  createCityRO(data: CityROCreateInput!): CityRO
  createCityROS(data: [CityROCreateInput!]!): [CityRO]
  updateCityRO(where: CityROWhereUniqueInput!, data: CityROUpdateInput!): CityRO
  updateCityROS(data: [CityROUpdateArgs!]!): [CityRO]
  deleteCityRO(where: CityROWhereUniqueInput!): CityRO
  deleteCityROS(where: [CityROWhereUniqueInput!]!): [CityRO]
  createCityIT(data: CityITCreateInput!): CityIT
  createCityITS(data: [CityITCreateInput!]!): [CityIT]
  updateCityIT(where: CityITWhereUniqueInput!, data: CityITUpdateInput!): CityIT
  updateCityITS(data: [CityITUpdateArgs!]!): [CityIT]
  deleteCityIT(where: CityITWhereUniqueInput!): CityIT
  deleteCityITS(where: [CityITWhereUniqueInput!]!): [CityIT]
  endSession: Boolean!
  authenticateUserWithPassword(email: String!, password: String!): UserAuthenticationWithPasswordResult
  createInitialUser(data: CreateInitialUserInput!): UserAuthenticationWithPasswordSuccess!
  sendUserPasswordResetLink(email: String!): Boolean!
  redeemUserPasswordResetToken(email: String!, token: String!, password: String!): RedeemUserPasswordResetTokenResult
}

union UserAuthenticationWithPasswordResult = UserAuthenticationWithPasswordSuccess | UserAuthenticationWithPasswordFailure

type UserAuthenticationWithPasswordSuccess {
  sessionToken: String!
  item: User!
}

type UserAuthenticationWithPasswordFailure {
  message: String!
}

input CreateInitialUserInput {
  name: String
  email: String
  password: String
}

type RedeemUserPasswordResetTokenResult {
  code: PasswordResetRedemptionErrorCode!
  message: String!
}

enum PasswordResetRedemptionErrorCode {
  FAILURE
  TOKEN_EXPIRED
  TOKEN_REDEEMED
}

type Query {
  users(where: UserWhereInput! = {}, orderBy: [UserOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: UserWhereUniqueInput): [User!]
  user(where: UserWhereUniqueInput!): User
  usersCount(where: UserWhereInput! = {}): Int
  blogs(where: BlogWhereInput! = {}, orderBy: [BlogOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: BlogWhereUniqueInput): [Blog!]
  blog(where: BlogWhereUniqueInput!): Blog
  blogsCount(where: BlogWhereInput! = {}): Int
  tags(where: TagWhereInput! = {}, orderBy: [TagOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TagWhereUniqueInput): [Tag!]
  tag(where: TagWhereUniqueInput!): Tag
  tagsCount(where: TagWhereInput! = {}): Int
  categories(where: CategoryWhereInput! = {}, orderBy: [CategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CategoryWhereUniqueInput): [Category!]
  category(where: CategoryWhereUniqueInput!): Category
  categoriesCount(where: CategoryWhereInput! = {}): Int
  subCategories(where: SubCategoryWhereInput! = {}, orderBy: [SubCategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: SubCategoryWhereUniqueInput): [SubCategory!]
  subCategory(where: SubCategoryWhereUniqueInput!): SubCategory
  subCategoriesCount(where: SubCategoryWhereInput! = {}): Int
  jobCategories(where: JobCategoryWhereInput! = {}, orderBy: [JobCategoryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: JobCategoryWhereUniqueInput): [JobCategory!]
  jobCategory(where: JobCategoryWhereUniqueInput!): JobCategory
  jobCategoriesCount(where: JobCategoryWhereInput! = {}): Int
  jobs(where: JobWhereInput! = {}, orderBy: [JobOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: JobWhereUniqueInput): [Job!]
  job(where: JobWhereUniqueInput!): Job
  jobsCount(where: JobWhereInput! = {}): Int
  jobApplications(where: JobApplicationWhereInput! = {}, orderBy: [JobApplicationOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: JobApplicationWhereUniqueInput): [JobApplication!]
  jobApplication(where: JobApplicationWhereUniqueInput!): JobApplication
  jobApplicationsCount(where: JobApplicationWhereInput! = {}): Int
  mediaGaleries(where: MediaGaleryWhereInput! = {}, orderBy: [MediaGaleryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MediaGaleryWhereUniqueInput): [MediaGalery!]
  mediaGalery(where: MediaGaleryWhereUniqueInput!): MediaGalery
  mediaGaleriesCount(where: MediaGaleryWhereInput! = {}): Int
  languages(where: LanguageWhereInput! = {}, orderBy: [LanguageOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LanguageWhereUniqueInput): [Language!]
  language(where: LanguageWhereUniqueInput!): Language
  languagesCount(where: LanguageWhereInput! = {}): Int
  contactForms(where: ContactFormWhereInput! = {}, orderBy: [ContactFormOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: ContactFormWhereUniqueInput): [ContactForm!]
  contactForm(where: ContactFormWhereUniqueInput!): ContactForm
  contactFormsCount(where: ContactFormWhereInput! = {}): Int
  medicalForms(where: MedicalFormWhereInput! = {}, orderBy: [MedicalFormOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: MedicalFormWhereUniqueInput): [MedicalForm!]
  medicalForm(where: MedicalFormWhereUniqueInput!): MedicalForm
  medicalFormsCount(where: MedicalFormWhereInput! = {}): Int
  transportForms(where: TransportFormWhereInput! = {}, orderBy: [TransportFormOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: TransportFormWhereUniqueInput): [TransportForm!]
  transportForm(where: TransportFormWhereUniqueInput!): TransportForm
  transportFormsCount(where: TransportFormWhereInput! = {}): Int
  employerForms(where: EmployerFormWhereInput! = {}, orderBy: [EmployerFormOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: EmployerFormWhereUniqueInput): [EmployerForm!]
  employerForm(where: EmployerFormWhereUniqueInput!): EmployerForm
  employerFormsCount(where: EmployerFormWhereInput! = {}): Int
  roles(where: RoleWhereInput! = {}, orderBy: [RoleOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: RoleWhereUniqueInput): [Role!]
  role(where: RoleWhereUniqueInput!): Role
  rolesCount(where: RoleWhereInput! = {}): Int
  locations(where: LocationWhereInput! = {}, orderBy: [LocationOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: LocationWhereUniqueInput): [Location!]
  location(where: LocationWhereUniqueInput!): Location
  locationsCount(where: LocationWhereInput! = {}): Int
  countries(where: CountryWhereInput! = {}, orderBy: [CountryOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CountryWhereUniqueInput): [Country!]
  country(where: CountryWhereUniqueInput!): Country
  countriesCount(where: CountryWhereInput! = {}): Int
  cityROS(where: CityROWhereInput! = {}, orderBy: [CityROOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CityROWhereUniqueInput): [CityRO!]
  cityRO(where: CityROWhereUniqueInput!): CityRO
  cityROSCount(where: CityROWhereInput! = {}): Int
  cityITS(where: CityITWhereInput! = {}, orderBy: [CityITOrderByInput!]! = [], take: Int, skip: Int! = 0, cursor: CityITWhereUniqueInput): [CityIT!]
  cityIT(where: CityITWhereUniqueInput!): CityIT
  cityITSCount(where: CityITWhereInput! = {}): Int
  keystone: KeystoneMeta!
  authenticatedItem: AuthenticatedItem
  validateUserPasswordResetToken(email: String!, token: String!): ValidateUserPasswordResetTokenResult
}

union AuthenticatedItem = User

type ValidateUserPasswordResetTokenResult {
  code: PasswordResetRedemptionErrorCode!
  message: String!
}

type KeystoneMeta {
  adminMeta: KeystoneAdminMeta!
}

type KeystoneAdminMeta {
  lists: [KeystoneAdminUIListMeta!]!
  list(key: String!): KeystoneAdminUIListMeta
}

type KeystoneAdminUIListMeta {
  key: String!
  itemQueryName: String!
  listQueryName: String!
  hideCreate: Boolean!
  hideDelete: Boolean!
  path: String!
  label: String!
  singular: String!
  plural: String!
  description: String
  initialColumns: [String!]!
  pageSize: Int!
  labelField: String!
  fields: [KeystoneAdminUIFieldMeta!]!
  groups: [KeystoneAdminUIFieldGroupMeta!]!
  initialSort: KeystoneAdminUISort
  isHidden: Boolean!
  isSingleton: Boolean!
}

type KeystoneAdminUIFieldMeta {
  path: String!
  label: String!
  description: String
  isOrderable: Boolean!
  isFilterable: Boolean!
  isNonNull: [KeystoneAdminUIFieldMetaIsNonNull!]
  fieldMeta: JSON
  viewsIndex: Int!
  customViewsIndex: Int
  createView: KeystoneAdminUIFieldMetaCreateView!
  listView: KeystoneAdminUIFieldMetaListView!
  itemView(id: ID): KeystoneAdminUIFieldMetaItemView
  search: QueryMode
}

enum KeystoneAdminUIFieldMetaIsNonNull {
  read
  create
  update
}

type KeystoneAdminUIFieldMetaCreateView {
  fieldMode: KeystoneAdminUIFieldMetaCreateViewFieldMode!
}

enum KeystoneAdminUIFieldMetaCreateViewFieldMode {
  edit
  hidden
}

type KeystoneAdminUIFieldMetaListView {
  fieldMode: KeystoneAdminUIFieldMetaListViewFieldMode!
}

enum KeystoneAdminUIFieldMetaListViewFieldMode {
  read
  hidden
}

type KeystoneAdminUIFieldMetaItemView {
  fieldMode: KeystoneAdminUIFieldMetaItemViewFieldMode
  fieldPosition: KeystoneAdminUIFieldMetaItemViewFieldPosition
}

enum KeystoneAdminUIFieldMetaItemViewFieldMode {
  edit
  read
  hidden
}

enum KeystoneAdminUIFieldMetaItemViewFieldPosition {
  form
  sidebar
}

enum QueryMode {
  default
  insensitive
}

type KeystoneAdminUIFieldGroupMeta {
  label: String!
  description: String
  fields: [KeystoneAdminUIFieldMeta!]!
}

type KeystoneAdminUISort {
  field: String!
  direction: KeystoneAdminUISortDirection!
}

enum KeystoneAdminUISortDirection {
  ASC
  DESC
}
