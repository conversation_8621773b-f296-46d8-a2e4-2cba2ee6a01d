{"name": "humansource-app", "version": "1.0.0", "private": true, "scripts": {"dev": "keystone dev", "start": "keystone start", "build": "keystone build", "postinstall": "keystone build --no-ui --frozen"}, "dependencies": {"@keystone-6/auth": "^8.0.0", "@keystone-6/core": "^6.1.0", "@keystone-6/fields-document": "^9.0.0", "@keystone-6/cloudinary": "^8.0.0", "@paralleldrive/cuid2": "^2.2.2", "@types/nodemailer": "^6.4.11", "typescript": "^5.4.5", "dotenv": "^16.3.1", "nodemailer": "^6.9.13"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "6.7.4", "@typescript-eslint/parser": "6.7.4", "babel-eslint": "^10.1.0", "eslint": "8.51.0", "eslint-config-airbnb": "19.0.4", "eslint-config-airbnb-typescript": "17.1.0", "eslint-config-prettier": "9.0.0", "eslint-plugin-html": "7.1.0", "eslint-plugin-import": "2.28.1", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-prettier": "5.0.0", "eslint-plugin-react": "7.33.2", "eslint-plugin-react-hooks": "4.6.0", "prettier": "3.0.3", "typescript": "5.2.2"}}